# Community Page Features

## Overview

The community page has been completely rebuilt with modern functionality including infinite scrolling, real-time interactions, and comprehensive comment management.

## Features Implemented

### 1. Infinite Scrolling

- **Hook**: `useInfiniteCommunityPosts` in `/src/lib/hooks/useInfiniteScroll.js`
- **Functionality**: Automatically loads more posts as user scrolls down
- **Data Sources**: Combines posts from Goals, Values, and Life Missions
- **Performance**: Uses Intersection Observer API for efficient scroll detection
- **Pagination**: Loads 10 posts per page from each source

### 2. Enhanced Post Cards

- **Component**: `PostCard` in `/src/components/community/PostCard.jsx`
- **Features**:
  - Like/Unlike functionality with real-time updates
  - Bookmark/Unbookmark functionality
  - Comment count display
  - Quick comment input
  - Author information with avatar
  - Post type indicators
  - Time stamps (relative time display)

### 3. Comment Management

- **Component**: `CommentModal` in `/src/components/community/CommentModal.jsx`
- **Features**:
  - Full-screen comment modal
  - Add new comments
  - Delete own comments
  - Real-time comment updates
  - Comment author information
  - Responsive design

### 4. Loading States & Error Handling

- **Initial Loading**: Skeleton placeholders while loading first posts
- **Loading More**: Inline loading indicator for additional posts
- **Error States**: Comprehensive error handling with retry functionality
- **Empty States**: User-friendly messages when no posts are available
- **Refresh Functionality**: Manual refresh button for latest content

## Technical Implementation

### Data Flow

1. **useInfiniteCommunityPosts** hook fetches data from three sources:
   - Goals (`usePublicGoals`)
   - Values (`usePublicValues`)
   - Life Missions (`usePublicLifeMissions`)

2. **Data Transformation**: Each source is transformed into a unified post format:

   ```javascript
   {
     id: 'goal-123', // Prefixed with type
     originalId: '123', // Original database ID
     type: 'goal', // Source type
     author: { name, id },
     content: 'Post content',
     description: 'Additional description',
     likes: 5, // Count of likes array length
     comments: 3, // Count of comments array length
     isLiked: false, // Determined by checking if current user ID is in likes array
     isBookmarked: false, // Determined by checking if current user ID is in bookmarks array
     createdAt: '2024-01-01T00:00:00Z',
     postTypeLabel: 'a goal'
   }
   ```

3. **Infinite Scroll**: Uses Intersection Observer to detect when user reaches bottom
4. **State Management**: React Query for caching and synchronization

### Hook Usage

```javascript
const {
  posts,           // Array of all loaded posts
  isLoading,       // Initial loading state
  isLoadingMore,   // Loading more posts state
  error,           // Any error that occurred
  hasMore,         // Whether more posts are available
  loadMore,        // Manual load more function
  reset,           // Reset to first page
  retry,           // Retry after error
  sentinelRef,     // Ref for infinite scroll trigger
  totalPosts,      // Total number of loaded posts
  currentPage      // Current page number
} = useInfiniteCommunityPosts();
```

### Interaction Hooks

Each post type has its own set of interaction hooks:

**Goals**:

- `useToggleLike` from `useGoal`
- `useToggleBookmark` from `useGoal`
- `useAddComment` from `useGoal`

**Values**:

- `useToggleLike` from `useValues`
- `useToggleBookmark` from `useValues`
- `useAddComment` from `useValues`

**Life Missions**:

- `useToggleLike` from `useLifeMissions`
- `useToggleBookmark` from `useLifeMissions`
- `useAddComment` from `useLifeMissions`

## Performance Optimizations

1. **Intersection Observer**: Efficient scroll detection
2. **React Query Caching**: Prevents unnecessary API calls
3. **Skeleton Loading**: Improves perceived performance
4. **Debounced Interactions**: Prevents spam clicking
5. **Optimistic Updates**: Immediate UI feedback
6. **Lazy Loading**: Only loads visible content

## User Experience Features

1. **Responsive Design**: Works on all device sizes
2. **Accessibility**: Proper ARIA labels and keyboard navigation
3. **Visual Feedback**: Loading states, hover effects, and animations
4. **Error Recovery**: Clear error messages with retry options
5. **Empty States**: Helpful messages when no content is available
6. **Real-time Updates**: Immediate reflection of user actions

## Future Enhancements

1. **Real-time Notifications**: WebSocket integration for live updates
2. **Advanced Filtering**: Filter by post type, author, or date
3. **Search Functionality**: Search within community posts
4. **User Profiles**: Click on authors to view their profiles
5. **Post Sharing**: Share posts to external platforms
6. **Rich Text Comments**: Support for formatted comments
7. **Image/Media Support**: Allow images in posts and comments
8. **Moderation Tools**: Report and moderate inappropriate content

## Testing

To test the community page:

1. **Start the development server**: `npm run dev`
2. **Navigate to**: `http://localhost:3000/community`
3. **Test Features**:
   - Scroll down to trigger infinite loading
   - Click like buttons to test interactions
   - Click comment buttons to open comment modal
   - Add comments and verify they appear
   - Test bookmark functionality
   - Try refresh button
   - Test error states (disconnect network)

## Dependencies Added

- `critters`: For CSS optimization
- `webpack-bundle-analyzer`: For bundle analysis (dev only)

## Files Modified/Created

### New Files

- `/src/lib/hooks/useInfiniteScroll.js` - Infinite scroll hook
- `/src/components/community/PostCard.jsx` - Enhanced post card
- `/src/components/community/CommentModal.jsx` - Comment management
- `/src/components/community/index.js` - Component exports
- `/docs/COMMUNITY_FEATURES.md` - This documentation

### Modified Files

- `/app/community/page.js` - Complete rewrite with new functionality
- `/src/lib/utils/performance.js` - Fixed server-side compatibility

## API Requirements

The community page expects the following API endpoints to be available:

1. **Public Goals**: `GET /api/goals/public`
2. **Public Values**: `GET /api/values/public`
3. **Public Life Missions**: `GET /api/life-missions/public`
4. **Like/Unlike**: `POST /api/{type}/{id}/like`
5. **Bookmark/Unbookmark**: `POST /api/{type}/{id}/bookmark`
6. **Add Comment**: `POST /api/{type}/{id}/comments`
7. **Delete Comment**: `DELETE /api/{type}/{id}/comments/{commentId}`

Each endpoint should support pagination parameters (`page`, `limit`, `sortBy`, `sortOrder`).

## Expected API Response Structure

### Goals Response

```javascript
{
  "success": true,
  "message": "Public goals retrieved successfully",
  "data": {
    "goals": [
      {
        "_id": "goal_id",
        "userId": { "_id": "user_id", "name": "User Name" },
        "title": "Goal title",
        "description": "Goal description",
        "likes": [], // Array of user IDs who liked
        "bookmarks": [], // Array of user IDs who bookmarked
        "comments": [], // Array of comment objects
        "createdAt": "2025-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 1,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### Values Response

```javascript
{
  "success": true,
  "message": "Public values retrieved successfully",
  "data": {
    "values": [
      {
        "_id": "value_id",
        "userId": { "_id": "user_id", "name": "User Name" },
        "name": "Value name",
        "definition": "Value definition",
        "likes": [],
        "bookmarks": [],
        "comments": [],
        "createdAt": "2025-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### Life Missions Response

```javascript
{
  "success": true,
  "data": [
    {
      "_id": "mission_id",
      "userId": { "_id": "user_id", "name": "User Name" },
      "mission": "Mission statement",
      "likes": [],
      "bookmarks": [],
      "comments": [],
      "createdAt": "2025-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total": 2,
    "page": 1,
    "limit": 10,
    "pages": 1
  }
}
```
