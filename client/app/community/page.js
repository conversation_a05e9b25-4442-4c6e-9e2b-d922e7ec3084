'use client';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import { usePublicGoals } from '@/src/lib/hooks/useGoal';
import { usePublicLifeMissions } from '@/src/lib/hooks/useLifeMissions';
import { usePublicValues } from '@/src/lib/hooks/useValues';
import { Bookmark, Heart, MessageSquare, MoreHorizontal } from 'lucide-react';
import React, { useState, useMemo } from 'react';

const PostCard = ({ post }) => (
	<div className='bg-white border-b border-gray-200 py-4 mb-4 rounded-md'>
		<div className='flex items-start space-x-3 px-4 mb-3'>
			<div className='w-12 h-12 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full overflow-hidden flex-shrink-0'>
				{/* Placeholder Avatar */}
				<img
					src={`https://ui-avatars.com/api/?name=${post.author.name}`}
					alt={post.author.name}
					className='w-full h-full object-cover'
				/>
			</div>
			<div className='flex-1'>
				<div className='flex items-center space-x-2'>
					<span className='font-semibold text-black'>{post.author.name}</span>
					<span className='text-gray-500 text-sm'>shared {post.type}</span>
				</div>
			</div>
		</div>

		<div className='px-4 mb-4'>
			<div className='bg-teal-50 border-2 border-teal-200 rounded-xl p-4'>
				<p className='text-gray-800'>{post.content}</p>
			</div>
		</div>

		<div className='flex items-center justify-between px-4'>
			<div className='flex items-center space-x-6'>
				<button className='flex items-center space-x-2 text-gray-600 hover:text-red-500'>
					<Heart className='w-5 h-5' />
					<span className='text-sm'>{post.likes}</span>
				</button>
				<button className='flex items-center space-x-2 text-gray-600 hover:text-blue-500'>
					<MessageSquare className='w-5 h-5' />
					<span className='text-sm'>{post.comments}</span>
				</button>
				<button className='text-gray-600 hover:text-gray-800'>
					<MoreHorizontal className='w-5 h-5' />
				</button>
			</div>
			<button className='text-gray-600 hover:text-yellow-500'>
				<Bookmark className='w-5 h-5' />
			</button>
		</div>
	</div>
);

const shuffleArray = (array) => {
	const shuffled = [...array];
	for (let i = shuffled.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
	}
	return shuffled;
};

const Community = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

	const filters = {
		page: 1,
		limit: 12,
		sortBy: 'createdAt',
		sortOrder: 'desc',
	};

	const { data: lifeMission } = usePublicLifeMissions({ page: 1, limit: 10 });
	const { data: values } = usePublicValues({ page: 1, limit: 10 });
	const { data: goals } = usePublicGoals(filters);

	const posts = useMemo(() => {
		const fromMissions =
			lifeMission?.data?.map((m) => ({
				id: m._id,
				author: {
					name: m.userId.name,
				},
				type: 'a life mission',
				content: m.mission,
				likes: m.likes?.length || 0,
				comments: m.comments?.length || 0,
			})) || [];

		const fromValues =
			values?.data?.values?.map((v) => ({
				id: v._id,
				author: {
					name: v.userId.name,
				},
				type: 'a core value',
				content: `${v.name}: ${v.definition}`,
				likes: v.likes?.length || 0,
				comments: v.comments?.length || 0,
			})) || [];

		const fromGoals =
			goals?.data?.goals?.map((g) => ({
				id: g._id,
				author: {
					name: g.userId.name,
				},
				type: 'a goal',
				content: g.title,
				likes: g.likes?.length || 0,
				comments: g.comments?.length || 0,
			})) || [];

		// Merge and shuffle them
		return shuffleArray([...fromMissions, ...fromValues, ...fromGoals]);
	}, [lifeMission, values, goals]);

	return (
		<div>
			<FixedHeader
				title='Community'
				toggleSidebar={toggleSidebar}
			/>
			<div className='max-w-2xl mx-auto'>
				{posts.length > 0 ? (
					posts.map((post) => (
						<PostCard
							key={post.id}
							post={post}
						/>
					))
				) : (
					<p className='text-center mt-10 text-gray-500'>
						No posts available yet.
					</p>
				)}
			</div>
			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Community;
