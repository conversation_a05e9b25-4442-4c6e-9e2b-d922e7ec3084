'use client';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import PostCard from '@/src/components/community/PostCard';
import CommentModal from '@/src/components/community/CommentModal';
import { useInfiniteCommunityPosts } from '@/src/lib/hooks/useInfiniteScroll';
import Loading from '@/src/components/common/Loading';
import { RefreshCw, AlertCircle, MessageSquare } from 'lucide-react';
import React, { useState } from 'react';

// Component removed - using imported PostCard instead

const Community = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [selectedPost, setSelectedPost] = useState(null);
	const [isCommentModalOpen, setIsCommentModalOpen] = useState(false);

	const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

	// Use infinite scroll hook
	const {
		posts,
		isLoading,
		isLoadingMore,
		error,
		hasMore,
		reset,
		sentinelRef,
	} = useInfiniteCommunityPosts();

	// Handle comment modal
	const handleCommentClick = (post) => {
		setSelectedPost(post);
		setIsCommentModalOpen(true);
	};

	const handleCloseCommentModal = () => {
		setIsCommentModalOpen(false);
		setSelectedPost(null);
	};

	// Handle refresh
	const handleRefresh = () => {
		reset();
	};

	// Error state
	if (error) {
		return (
			<div>
				<FixedHeader
					title='Community'
					toggleSidebar={toggleSidebar}
				/>
				<div className='max-w-2xl mx-auto px-4 py-8'>
					<div className='text-center'>
						<AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
						<h3 className='text-lg font-medium text-gray-900 mb-2'>
							Something went wrong
						</h3>
						<p className='text-gray-600 mb-4'>
							We couldn't load the community posts. Please try again.
						</p>
						<button
							onClick={handleRefresh}
							className='inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors'>
							<RefreshCw className='w-4 h-4 mr-2' />
							Try Again
						</button>
					</div>
				</div>
				<Sidebar
					isOpen={isSidebarOpen}
					onToggle={toggleSidebar}
				/>
			</div>
		);
	}

	return (
		<div>
			<FixedHeader
				title='Community'
				toggleSidebar={toggleSidebar}
			/>

			<div className='max-w-2xl mx-auto px-4'>
				{/* Refresh Button */}
				<div className='py-4 border-b border-gray-200 mb-4'>
					<button
						onClick={handleRefresh}
						className='inline-flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors'>
						<RefreshCw className='w-4 h-4 mr-2' />
						Refresh
					</button>
				</div>

				{/* Initial Loading */}
				{isLoading ? (
					<div className='space-y-4'>
						{[...Array(3)].map((_, i) => (
							<div
								key={i}
								className='bg-white border border-gray-200 rounded-lg p-4'>
								<Loading.Skeleton lines={3} />
							</div>
						))}
					</div>
				) : posts.length > 0 ? (
					<>
						{/* Posts */}
						{posts.map((post) => (
							<PostCard
								key={post.id}
								post={post}
								onCommentClick={handleCommentClick}
							/>
						))}

						{/* Loading More Indicator */}
						{isLoadingMore && (
							<div className='py-8 text-center'>
								<Loading.Inline message='Loading more posts...' />
							</div>
						)}

						{/* Infinite Scroll Sentinel */}
						{hasMore && (
							<div
								ref={sentinelRef}
								className='h-10 flex items-center justify-center'>
								{/* This div triggers loading when it comes into view */}
							</div>
						)}

						{/* End of Posts Message */}
						{!hasMore && posts.length > 0 && (
							<div className='py-8 text-center text-gray-500'>
								<p>You've reached the end! 🎉</p>
								<p className='text-sm mt-1'>Check back later for new posts.</p>
							</div>
						)}
					</>
				) : (
					<div className='text-center py-12'>
						<div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4'>
							<MessageSquare className='w-8 h-8 text-gray-400' />
						</div>
						<h3 className='text-lg font-medium text-gray-900 mb-2'>
							No posts yet
						</h3>
						<p className='text-gray-600 mb-4'>
							Be the first to share your goals, values, or life missions with
							the community!
						</p>
						<button
							onClick={handleRefresh}
							className='inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors'>
							<RefreshCw className='w-4 h-4 mr-2' />
							Refresh
						</button>
					</div>
				)}
			</div>

			{/* Comment Modal */}
			<CommentModal
				post={selectedPost}
				isOpen={isCommentModalOpen}
				onClose={handleCloseCommentModal}
			/>

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Community;
