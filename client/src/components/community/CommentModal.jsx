import React, { useState, useEffect } from 'react';
import { X, Send, Trash2, Heart, MessageSquare } from 'lucide-react';
import { useGoalById } from '@/src/lib/hooks/useGoal';
import { useValueById } from '@/src/lib/hooks/useValues';
import { useLifeMissionById } from '@/src/lib/hooks/useLifeMissions';
import {
	useAddComment as useAddGoalComment,
	useDeleteComment as useDeleteGoalComment,
} from '@/src/lib/hooks/useGoal';
import {
	useAddComment as useAddValueComment,
	useDeleteComment as useDeleteValueComment,
} from '@/src/lib/hooks/useValues';
import {
	useAddComment as useAddMissionComment,
	useDeleteComment as useDeleteMissionComment,
} from '@/src/lib/hooks/useLifeMissions';
import toast from 'react-hot-toast';

const CommentModal = ({ post, isOpen, onClose }) => {
	const [commentText, setCommentText] = useState('');
	const [isSubmittingComment, setIsSubmittingComment] = useState(false);

	// Get the appropriate hooks based on post type
	const getDetailHook = () => {
		switch (post?.type) {
			case 'goal':
				return useGoalById;
			case 'value':
				return useValueById;
			case 'lifeMission':
				return useLifeMissionById;
			default:
				return useGoalById;
		}
	};

	const getCommentHooks = () => {
		switch (post?.type) {
			case 'goal':
				return {
					useAddComment: useAddGoalComment,
					useDeleteComment: useDeleteGoalComment,
				};
			case 'value':
				return {
					useAddComment: useAddValueComment,
					useDeleteComment: useDeleteValueComment,
				};
			case 'lifeMission':
				return {
					useAddComment: useAddMissionComment,
					useDeleteComment: useDeleteMissionComment,
				};
			default:
				return {
					useAddComment: useAddGoalComment,
					useDeleteComment: useDeleteGoalComment,
				};
		}
	};

	const useDetailHook = getDetailHook();
	const { useAddComment, useDeleteComment } = getCommentHooks();

	// Fetch detailed post data with comments
	const { data: detailData, isLoading: isLoadingDetail } = useDetailHook(
		post?.originalId,
	);

	// Initialize mutations
	const addCommentMutation = useAddComment();
	const deleteCommentMutation = useDeleteComment();

	// Get comments from the detailed data
	const comments = detailData?.data?.comments || detailData?.comments || [];

	// Handle comment submission
	const handleCommentSubmit = async (e) => {
		e.preventDefault();
		if (!commentText.trim()) return;

		setIsSubmittingComment(true);
		try {
			await addCommentMutation.mutateAsync({
				id: post.originalId,
				text: commentText.trim(),
			});
			setCommentText('');
			toast.success('Comment added!');
		} catch (error) {
			toast.error('Failed to add comment');
			console.error('Error adding comment:', error);
		} finally {
			setIsSubmittingComment(false);
		}
	};

	// Handle comment deletion
	const handleDeleteComment = async (commentId) => {
		if (!window.confirm('Are you sure you want to delete this comment?'))
			return;

		try {
			await deleteCommentMutation.mutateAsync({
				id: post.originalId,
				commentId,
			});
			toast.success('Comment deleted!');
		} catch (error) {
			toast.error('Failed to delete comment');
			console.error('Error deleting comment:', error);
		}
	};

	// Format time ago
	const formatTimeAgo = (dateString) => {
		const date = new Date(dateString);
		const now = new Date();
		const diffInSeconds = Math.floor((now - date) / 1000);

		if (diffInSeconds < 60) return 'just now';
		if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
		if (diffInSeconds < 86400)
			return `${Math.floor(diffInSeconds / 3600)}h ago`;
		if (diffInSeconds < 604800)
			return `${Math.floor(diffInSeconds / 86400)}d ago`;
		return date.toLocaleDateString();
	};

	// Close modal on escape key
	useEffect(() => {
		const handleEscape = (e) => {
			if (e.key === 'Escape') onClose();
		};

		if (isOpen) {
			document.addEventListener('keydown', handleEscape);
			document.body.style.overflow = 'hidden';
		}

		return () => {
			document.removeEventListener('keydown', handleEscape);
			document.body.style.overflow = 'unset';
		};
	}, [isOpen, onClose]);

	if (!isOpen || !post) return null;

	return (
		<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
			<div className='bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col'>
				{/* Header */}
				<div className='flex items-center justify-between p-4 border-b border-gray-200'>
					<h2 className='text-lg font-semibold text-gray-900'>Comments</h2>
					<button
						onClick={onClose}
						className='text-gray-400 hover:text-gray-600 p-1'>
						<X className='w-6 h-6' />
					</button>
				</div>

				{/* Post Preview */}
				<div className='p-4 border-b border-gray-100 bg-gray-50'>
					<div className='flex items-start space-x-3'>
						<div className='w-10 h-10 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full overflow-hidden flex-shrink-0'>
							<img
								src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
									post.author.name,
								)}&background=random`}
								alt={post.author.name}
								className='w-full h-full object-cover'
							/>
						</div>
						<div className='flex-1'>
							<div className='flex items-center space-x-2 mb-1'>
								<span className='font-medium text-gray-900'>
									{post.author.name}
								</span>
								<span className='text-gray-500 text-sm'>
									shared {post.postTypeLabel}
								</span>
							</div>
							<p className='text-gray-800 text-sm'>{post.content}</p>
							{post.description && (
								<p className='text-gray-600 text-xs mt-1'>{post.description}</p>
							)}
						</div>
					</div>
				</div>

				{/* Comments List */}
				<div className='flex-1 overflow-y-auto p-4'>
					{isLoadingDetail ? (
						<div className='flex items-center justify-center py-8'>
							<div className='w-6 h-6 border-2 border-teal-600 border-t-transparent rounded-full animate-spin' />
							<span className='ml-2 text-gray-600'>Loading comments...</span>
						</div>
					) : comments.length > 0 ? (
						<div className='space-y-4'>
							{comments.map((comment) => (
								<div
									key={comment._id}
									className='flex items-start space-x-3'>
									<div className='w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full overflow-hidden flex-shrink-0'>
										<img
											src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
												comment.userId?.name || 'Anonymous',
											)}&background=random`}
											alt={comment.userId?.name || 'Anonymous'}
											className='w-full h-full object-cover'
										/>
									</div>
									<div className='flex-1'>
										<div className='bg-gray-100 rounded-lg px-3 py-2'>
											<div className='flex items-center justify-between mb-1'>
												<span className='font-medium text-sm text-gray-900'>
													{comment.userId?.name || 'Anonymous'}
												</span>
												{comment.userId?._id === 'current-user-id' && (
													<button
														onClick={() => handleDeleteComment(comment._id)}
														className='text-gray-400 hover:text-red-500 p-1'
														disabled={deleteCommentMutation.isLoading}>
														<Trash2 className='w-3 h-3' />
													</button>
												)}
											</div>
											<p className='text-gray-800 text-sm'>{comment.text}</p>
										</div>
										<div className='flex items-center space-x-4 mt-1 ml-3'>
											<span className='text-xs text-gray-500'>
												{formatTimeAgo(comment.createdAt)}
											</span>
										</div>
									</div>
								</div>
							))}
						</div>
					) : (
						<div className='text-center py-8 text-gray-500'>
							<MessageSquare className='w-12 h-12 mx-auto mb-2 text-gray-300' />
							<p>No comments yet. Be the first to comment!</p>
						</div>
					)}
				</div>

				{/* Comment Input */}
				<div className='p-4 border-t border-gray-200'>
					<form
						onSubmit={handleCommentSubmit}
						className='flex space-x-3'>
						<div className='w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex-shrink-0'>
							<img
								src={`https://ui-avatars.com/api/?name=You&background=random`}
								alt='You'
								className='w-full h-full object-cover rounded-full'
							/>
						</div>
						<div className='flex-1 flex space-x-2'>
							<input
								type='text'
								value={commentText}
								onChange={(e) => setCommentText(e.target.value)}
								placeholder='Write a comment...'
								className='flex-1 px-3 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent'
								disabled={isSubmittingComment}
							/>
							<button
								type='submit'
								disabled={!commentText.trim() || isSubmittingComment}
								className='px-4 py-2 bg-teal-600 text-white rounded-full hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'>
								{isSubmittingComment ? (
									<div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
								) : (
									<Send className='w-4 h-4' />
								)}
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	);
};

export default CommentModal;
