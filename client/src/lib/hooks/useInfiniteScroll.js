import { useState, useEffect, useCallback, useMemo } from 'react';
import { useIntersectionObserver } from '../utils/performance';
import { usePublicGoals } from './useGoal';
import { usePublicValues } from './useValues';
import { usePublicLifeMissions } from './useLifeMissions';

// Helper function to check if current user has liked/bookmarked an item
const checkUserInteraction = (interactionArray, currentUserId) => {
	if (!interactionArray || !currentUserId) return false;
	return interactionArray.some(
		(item) =>
			(typeof item === 'string' ? item : item._id || item.userId) ===
			currentUserId,
	);
};

/**
 * Custom hook for infinite scrolling community posts
 * Combines data from goals, values, and life missions with infinite scroll functionality
 */
export const useInfiniteCommunityPosts = () => {
	const [page, setPage] = useState(1);
	const [allPosts, setAllPosts] = useState([]);
	const [hasMore, setHasMore] = useState(true);
	const [isLoadingMore, setIsLoadingMore] = useState(false);

	const limit = 10; // Posts per page

	// Get current user ID from localStorage (you might want to get this from a user context instead)
	const currentUserId =
		typeof window !== 'undefined'
			? JSON.parse(localStorage.getItem('user') || '{}')?.id ||
			  JSON.parse(localStorage.getItem('user') || '{}')?.userId ||
			  JSON.parse(localStorage.getItem('user') || '{}')?._id
			: null;

	// Fetch data from all three sources
	const {
		data: goalsData,
		isLoading: goalsLoading,
		error: goalsError,
	} = usePublicGoals({
		page,
		limit,
		sortBy: 'createdAt',
		sortOrder: 'desc',
	});

	const {
		data: valuesData,
		isLoading: valuesLoading,
		error: valuesError,
	} = usePublicValues({
		page,
		limit,
		sortBy: 'createdAt',
		sortOrder: 'desc',
	});

	const {
		data: lifeMissionsData,
		isLoading: lifeMissionsLoading,
		error: lifeMissionsError,
	} = usePublicLifeMissions({
		page,
		limit,
		sortBy: 'createdAt',
		sortOrder: 'desc',
	});

	// Transform data into unified post format
	const transformedPosts = useMemo(() => {
		const posts = [];

		// Transform goals
		if (goalsData?.data?.goals) {
			const goalPosts = goalsData.data.goals.map((goal) => ({
				id: `goal-${goal._id}`,
				originalId: goal._id,
				type: 'goal',
				author: {
					name: goal.userId?.name || 'Anonymous',
					id: goal.userId?._id,
				},
				content: goal.title,
				description: goal.description || '',
				likes: goal.likes?.length || 0,
				comments: goal.comments?.length || 0,
				isLiked: checkUserInteraction(goal.likes, currentUserId),
				isBookmarked: checkUserInteraction(goal.bookmarks, currentUserId),
				createdAt: goal.createdAt,
				postTypeLabel: 'a goal',
			}));
			posts.push(...goalPosts);
		}

		// Transform values
		if (valuesData?.data?.values) {
			const valuePosts = valuesData.data.values.map((value) => ({
				id: `value-${value._id}`,
				originalId: value._id,
				type: 'value',
				author: {
					name: value.userId?.name || 'Anonymous',
					id: value.userId?._id,
				},
				content: value.name,
				description: value.definition || '',
				likes: value.likes?.length || 0,
				comments: value.comments?.length || 0,
				isLiked: checkUserInteraction(value.likes, currentUserId),
				isBookmarked: checkUserInteraction(value.bookmarks, currentUserId),
				createdAt: value.createdAt,
				postTypeLabel: 'a core value',
			}));
			posts.push(...valuePosts);
		}

		// Transform life missions
		if (lifeMissionsData?.data) {
			const missionPosts = lifeMissionsData.data.map((mission) => ({
				id: `mission-${mission._id}`,
				originalId: mission._id,
				type: 'lifeMission',
				author: {
					name: mission.userId?.name || 'Anonymous',
					id: mission.userId?._id,
				},
				content: mission.mission,
				description: '', // Life missions don't seem to have a description field
				likes: mission.likes?.length || 0,
				comments: mission.comments?.length || 0,
				isLiked: checkUserInteraction(mission.likes, currentUserId),
				isBookmarked: checkUserInteraction(mission.bookmarks, currentUserId),
				createdAt: mission.createdAt,
				postTypeLabel: 'a life mission',
			}));
			posts.push(...missionPosts);
		}

		// Sort by creation date (newest first)
		return posts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
	}, [goalsData, valuesData, lifeMissionsData]);

	// Update posts when new data arrives
	useEffect(() => {
		if (transformedPosts.length > 0) {
			if (page === 1) {
				setAllPosts(transformedPosts);
			} else {
				setAllPosts((prev) => {
					// Avoid duplicates by filtering out posts that already exist
					const existingIds = new Set(prev.map((post) => post.id));
					const newPosts = transformedPosts.filter(
						(post) => !existingIds.has(post.id),
					);
					return [...prev, ...newPosts];
				});
			}

			// Check if we have more data to load based on pagination info
			const goalsPagination = goalsData?.data?.pagination;
			const valuesPagination = valuesData?.data?.pagination;
			const missionsPagination = lifeMissionsData?.pagination;

			const hasMoreGoals =
				goalsPagination?.hasNext ||
				goalsPagination?.page < goalsPagination?.totalPages;
			const hasMoreValues =
				valuesPagination?.hasNext ||
				valuesPagination?.page < valuesPagination?.totalPages;
			const hasMoreMissions =
				missionsPagination?.page < missionsPagination?.pages;

			// We have more data if any of the sources has more data
			setHasMore(hasMoreGoals || hasMoreValues || hasMoreMissions);
			setIsLoadingMore(false);
		}
	}, [transformedPosts, page, limit, goalsData, valuesData, lifeMissionsData]);

	// Loading states
	const isInitialLoading =
		page === 1 && (goalsLoading || valuesLoading || lifeMissionsLoading);

	// Error handling
	const error = goalsError || valuesError || lifeMissionsError;

	// Load more function
	const loadMore = useCallback(() => {
		if (!isLoadingMore && hasMore && !isInitialLoading) {
			setIsLoadingMore(true);
			setPage((prev) => prev + 1);
		}
	}, [isLoadingMore, hasMore, isInitialLoading]);

	// Intersection observer for infinite scroll
	const [sentinelRef, isIntersecting] = useIntersectionObserver({
		threshold: 0.1,
		rootMargin: '100px',
	});

	// Trigger load more when sentinel is visible
	useEffect(() => {
		if (isIntersecting && hasMore && !isLoadingMore && !isInitialLoading) {
			loadMore();
		}
	}, [isIntersecting, hasMore, isLoadingMore, isInitialLoading, loadMore]);

	// Reset function for refreshing
	const reset = useCallback(() => {
		setPage(1);
		setAllPosts([]);
		setHasMore(true);
		setIsLoadingMore(false);
	}, []);

	// Retry function for error recovery
	const retry = useCallback(() => {
		if (error) {
			reset();
		}
	}, [error, reset]);

	return {
		posts: allPosts,
		isLoading: isInitialLoading,
		isLoadingMore,
		error,
		hasMore,
		loadMore,
		reset,
		retry,
		sentinelRef,
		// Additional metadata
		totalPosts: allPosts.length,
		currentPage: page,
	};
};
