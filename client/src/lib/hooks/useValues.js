import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { setAuthToken } from '../api/client';
import { valuesService } from '../api/values';
import { isAuthenticated, logout } from './useAuth';

// Get user's values
export const useUserValues = (query = {}) => {
	return useQuery({
		queryKey: ['values', 'user', query],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.getUserValues(query);
		},
		enabled: isAuthenticated(),
		staleTime: 60 * 1000, // 1 minute
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching user values:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get public values
export const usePublicValues = (query = {}) => {
	return useQuery({
		queryKey: ['values', 'public', query],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.getPublicValues(query);
		},
		enabled: isAuthenticated(),
		staleTime: 60 * 1000, // 1 minute
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching public values:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get specific value by ID
export const useValueById = (id) => {
	return useQuery({
		queryKey: ['values', id],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.getValueById(id);
		},
		enabled: isAuthenticated() && !!id,
		staleTime: 30 * 1000, // 30 seconds
		gcTime: 5 * 60 * 1000, // 5 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching value:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Create value mutation
export const useCreateValue = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (valueData) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.createValue(valueData);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['values'] });
			console.log('Value created successfully:', data);
		},
		onError: (error) => {
			console.error('Error creating value:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Update value mutation
export const useUpdateValue = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, valueData }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.updateValue(id, valueData);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['values', variables.id] });
			queryClient.invalidateQueries({ queryKey: ['values'] });
			console.log('Value updated successfully:', data);
		},
		onError: (error) => {
			console.error('Error updating value:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete value mutation
export const useDeleteValue = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.deleteValue(id);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['values'] });
			console.log('Value deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting value:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Toggle like mutation
export const useToggleLike = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.toggleLike(id);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific value and all values queries
			queryClient.invalidateQueries({ queryKey: ['values', variables] });
			queryClient.invalidateQueries({ queryKey: ['values'] });
			console.log('Like toggled successfully:', data);
		},
		onError: (error) => {
			console.error('Error toggling like:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Toggle bookmark mutation
export const useToggleBookmark = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.toggleBookmark(id);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific value and all values queries
			queryClient.invalidateQueries({ queryKey: ['values', variables] });
			queryClient.invalidateQueries({ queryKey: ['values'] });
			console.log('Bookmark toggled successfully:', data);
		},
		onError: (error) => {
			console.error('Error toggling bookmark:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Add comment mutation
export const useAddComment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, text }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.addComment(id, text);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific value to show new comment
			queryClient.invalidateQueries({
				queryKey: ['values', variables.id],
			});
			console.log('Comment added successfully:', data);
		},
		onError: (error) => {
			console.error('Error adding comment:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete comment mutation
export const useDeleteComment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, commentId }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await valuesService.deleteComment(id, commentId);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific value to remove deleted comment
			queryClient.invalidateQueries({
				queryKey: ['values', variables.id],
			});
			console.log('Comment deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting comment:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};
