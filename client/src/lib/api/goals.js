/**
 * Goals API service
 */
import { api, createQueryString } from './client';

export const goalsService = {
	getUserGoals: async (params = {}) => {
		const queryString = createQueryString(params);
		const url = queryString ? `/api/goals?${queryString}` : '/api/goals';
		const response = await api.get(url);
		return response.data;
	},

	getPublicGoals: async (params = {}) => {
		const queryString = createQueryString(params);
		const url = queryString
			? `/api/goals/public?${queryString}`
			: '/api/goals/public';
		const response = await api.get(url);
		return response.data;
	},

	getGoalById: async (id) => {
		const response = await api.get(`/api/goals/${id}`);
		return response.data;
	},

	createGoal: async (goalData) => {
		const response = await api.post('/api/goals', goalData);
		return response.data;
	},

	updateGoal: async (id, goalData) => {
		const response = await api.put(`/api/goals/${id}`, goalData);
		return response.data;
	},

	deleteGoal: async (id) => {
		const response = await api.delete(`/api/goals/${id}`);
		return response.data;
	},

	addTask: async (goalId, taskData) => {
		const response = await api.post(`/api/goals/${goalId}/tasks`, taskData);
		return response.data;
	},

	updateTask: async (goalId, taskId, taskData) => {
		const response = await api.put(
			`/api/goals/${goalId}/tasks/${taskId}`,
			taskData,
		);
		return response.data;
	},

	deleteTask: async (goalId, taskId) => {
		const response = await api.delete(`/api/goals/${goalId}/tasks/${taskId}`);
		return response.data;
	},

	completeTask: async (goalId, taskId) => {
		const response = await api.patch(
			`/api/goals/${goalId}/tasks/${taskId}/complete`,
		);
		return response.data;
	},

	getGoalStats: async () => {
		const response = await api.get('/api/goals/stats');
		return response.data;
	},

	/**
	 * Like or unlike a goal
	 * @param {string} goalId - Goal ID
	 * @returns {Promise} API response
	 */
	toggleLike: async (goalId) => {
		const response = await api.put(`/api/goals/${goalId}/like`);
		return response.data;
	},

	/**
	 * Bookmark or unbookmark a goal
	 * @param {string} goalId - Goal ID
	 * @returns {Promise} API response
	 */
	toggleBookmark: async (goalId) => {
		const response = await api.put(`/api/goals/${goalId}/bookmark`);
		return response.data;
	},

	/**
	 * Add a comment to a goal
	 * @param {string} goalId - Goal ID
	 * @param {string} text - Comment text
	 * @returns {Promise} API response
	 */
	addComment: async (goalId, text) => {
		const response = await api.post(`/api/goals/${goalId}/comment`, { text });
		return response.data;
	},
	/**
	 * Delete a comment from a goal
	 * @param {string} goalId - Goal ID
	 * @param {string} commentId - Comment ID
	 * @returns {Promise} API response
	 */
	deleteComment: async (goalId, commentId) => {
		const response = await api.delete(
			`/api/goals/${goalId}/comment/${commentId}`,
		);
		return response.data;
	},
};
