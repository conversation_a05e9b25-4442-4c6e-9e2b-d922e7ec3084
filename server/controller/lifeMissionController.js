const LifeMission = require('../models/LifeMission');

// Utility: Check if user is authorized
const isAuthorized = (user) => {
	// You can modify this logic based on roles/permissions
	return user && user._id;
};

// Create LifeMission
exports.createLifeMission = async (req, res) => {
	try {
		if (!isAuthorized(req.user)) {
			return res.status(403).json({ message: 'Unauthorized' });
		}
		const { mission, isPublic = false } = req.body;
		const userId = req.user._id;

		const lifeMission = new LifeMission({
			userId,
			mission: mission.trim(),
			isPublic,
		});
		await lifeMission.save();

		res.status(201).json({
			success: true,
			message: 'LifeMission created successfully',
			data: { lifeMission },
		});
	} catch (error) {
		console.error('Create Error:', error);
		res
			.status(500)
			.json({ success: false, message: 'Failed to create lifeMission' });
	}
};

// Get all life missions for current user (with pagination & search)
exports.getUserLifeMissions = async (req, res) => {
	try {
		if (!isAuthorized(req.user)) {
			return res.status(403).json({ message: 'Unauthorized' });
		}

		const { page = 1, limit = 10, search = '' } = req.query;
		const skip = (page - 1) * limit;

		const query = {
			userId: req.user._id,
			mission: { $regex: search, $options: 'i' },
		};

		const total = await LifeMission.countDocuments(query);
		const missions = await LifeMission.find(query)
			.sort({ createdAt: -1 })
			.skip(skip)
			.limit(Number(limit));

		res.status(200).json({
			success: true,
			data: missions,
			pagination: {
				total,
				page: Number(page),
				limit: Number(limit),
				pages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		console.error('Get User LifeMissions Error:', error);
		res
			.status(500)
			.json({ success: false, message: 'Failed to get life missions' });
	}
};

// Get all public life missions (all users, with pagination & search)
exports.getPublicLifeMissions = async (req, res) => {
	try {
		const { page = 1, limit = 10, search = '' } = req.query;
		const skip = (page - 1) * limit;

		const query = {
			isPublic: true,
			mission: { $regex: search, $options: 'i' },
		};

		const total = await LifeMission.countDocuments(query);
		const missions = await LifeMission.find(query)
			.sort({ createdAt: -1 })
			.skip(skip)
			.limit(Number(limit))
			.populate('userId', 'name email'); // optionally show user info

		res.status(200).json({
			success: true,
			data: missions,
			pagination: {
				total,
				page: Number(page),
				limit: Number(limit),
				pages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		console.error('Get Public Missions Error:', error);
		res
			.status(500)
			.json({ success: false, message: 'Failed to fetch public missions' });
	}
};

// Get life mission by ID
exports.getLifeMissionById = async (req, res) => {
	try {
		const { id } = req.params;

		const mission = await LifeMission.findById(id);
		if (!mission) {
			return res
				.status(404)
				.json({ success: false, message: 'LifeMission not found' });
		}

		// Allow access if public or belongs to current user
		if (
			!mission.isPublic &&
			mission.userId.toString() !== req.user._id.toString()
		) {
			return res.status(403).json({ message: 'Unauthorized' });
		}

		res.status(200).json({ success: true, data: mission });
	} catch (error) {
		console.error('Get LifeMission by ID Error:', error);
		res
			.status(500)
			.json({ success: false, message: 'Failed to get life mission' });
	}
};

// Delete a life mission
exports.deleteLifeMission = async (req, res) => {
	try {
		const { id } = req.params;
		const mission = await LifeMission.findById(id);

		if (!mission) {
			return res
				.status(404)
				.json({ success: false, message: 'LifeMission not found' });
		}

		if (mission.userId.toString() !== req.user._id.toString()) {
			return res
				.status(403)
				.json({ message: 'Unauthorized to delete this life mission' });
		}

		await mission.deleteOne();

		res
			.status(200)
			.json({ success: true, message: 'LifeMission deleted successfully' });
	} catch (error) {
		console.error('Delete Error:', error);
		res
			.status(500)
			.json({ success: false, message: 'Failed to delete life mission' });
	}
};

exports.toggleLike = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const mission = await LifeMission.findById(id);
		if (!mission)
			return res.status(404).json({ message: 'LifeMission not found' });

		const alreadyLiked = mission.likes.includes(userId);
		if (alreadyLiked) {
			mission.likes.pull(userId);
		} else {
			mission.likes.push(userId);
		}
		await mission.save();

		res.status(200).json({
			success: true,
			liked: !alreadyLiked,
			totalLikes: mission.likes.length,
		});
	} catch (err) {
		console.error('Toggle Like Error:', err);
		res.status(500).json({ success: false, message: 'Failed to like/unlike' });
	}
};

exports.toggleBookmark = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const mission = await LifeMission.findById(id);
		if (!mission)
			return res.status(404).json({ message: 'LifeMission not found' });

		const bookmarked = mission.bookmarks.includes(userId);
		if (bookmarked) {
			mission.bookmarks.pull(userId);
		} else {
			mission.bookmarks.push(userId);
		}
		await mission.save();

		res.status(200).json({
			success: true,
			bookmarked: !bookmarked,
			totalBookmarks: mission.bookmarks.length,
		});
	} catch (err) {
		console.error('Toggle Bookmark Error:', err);
		res
			.status(500)
			.json({ success: false, message: 'Failed to bookmark/unbookmark' });
	}
};

exports.addComment = async (req, res) => {
	try {
		const { id } = req.params;
		const { text } = req.body;
		const userId = req.user._id;

		if (!text || text.trim().length < 1) {
			return res.status(400).json({ message: 'Comment text is required' });
		}

		const mission = await LifeMission.findById(id);
		if (!mission)
			return res.status(404).json({ message: 'LifeMission not found' });

		mission.comments.push({ userId, text: text.trim() });
		await mission.save();

		const populatedMission = await LifeMission.findById(id).populate({
			path: 'comments.userId',
			select: 'name',
		});

		res.status(201).json({
			success: true,
			message: 'Comment added',
			comments: populatedMission.comments,
		});
	} catch (err) {
		console.error('Add Comment Error:', err);
		res.status(500).json({ success: false, message: 'Failed to add comment' });
	}
};

exports.deleteComment = async (req, res) => {
	try {
		const { id, commentId } = req.params;
		const userId = req.user._id;

		const mission = await LifeMission.findById(id);
		if (!mission)
			return res.status(404).json({ message: 'LifeMission not found' });

		const comment = mission.comments.id(commentId);
		if (!comment) return res.status(404).json({ message: 'Comment not found' });

		if (comment.userId.toString() !== userId.toString()) {
			return res
				.status(403)
				.json({ message: 'Unauthorized to delete this comment' });
		}

		comment.remove();
		await mission.save();

		res.status(200).json({ success: true, message: 'Comment deleted' });
	} catch (err) {
		console.error('Delete Comment Error:', err);
		res
			.status(500)
			.json({ success: false, message: 'Failed to delete comment' });
	}
};
